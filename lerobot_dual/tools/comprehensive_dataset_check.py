#!/usr/bin/env python

"""
全面检查数据集一致性的脚本
检查所有文件类型：parquet, videos, metadata files
"""

import pandas as pd
import json
from pathlib import Path

def check_dataset_consistency(dataset_root):
    """全面检查数据集一致性"""
    
    dataset_root = Path(dataset_root)
    print("=== 数据集一致性全面检查 ===")
    print(f"数据集路径: {dataset_root}")
    
    issues = []
    
    # 1. 读取metadata
    print("\n1. 读取metadata文件...")
    
    # info.json
    info_path = dataset_root / "meta/info.json"
    with open(info_path, 'r') as f:
        info = json.load(f)
    
    # episodes.jsonl
    episodes_path = dataset_root / "meta/episodes.jsonl"
    episodes = {}
    with open(episodes_path, 'r') as f:
        for line in f:
            ep = json.loads(line)
            episodes[ep["episode_index"]] = ep
    
    # episodes_stats.jsonl
    stats_path = dataset_root / "meta/episodes_stats.jsonl"
    episodes_stats = {}
    with open(stats_path, 'r') as f:
        for line in f:
            stat = json.loads(line)
            episodes_stats[stat["episode_index"]] = stat
    
    # tasks.jsonl
    tasks_path = dataset_root / "meta/tasks.jsonl"
    tasks = {}
    with open(tasks_path, 'r') as f:
        for line in f:
            task = json.loads(line)
            tasks[task["task_index"]] = task
    
    print(f"  info.json: total_episodes={info['total_episodes']}, total_frames={info['total_frames']}, total_tasks={info['total_tasks']}")
    print(f"  episodes.jsonl: {len(episodes)} episodes")
    print(f"  episodes_stats.jsonl: {len(episodes_stats)} stats")
    print(f"  tasks.jsonl: {len(tasks)} tasks")
    
    # 2. 检查文件数量一致性
    print("\n2. 检查文件数量一致性...")
    
    # Parquet files
    data_dir = dataset_root / "data/chunk-000"
    parquet_files = sorted(data_dir.glob("episode_*.parquet"))
    
    # Video files
    video_dir = dataset_root / "videos/chunk-000"
    video_keys = ["observation.images.top", "observation.images.left_wrist", "observation.images.right_wrist"]
    video_counts = {}
    for video_key in video_keys:
        video_files = list((video_dir / video_key).glob("episode_*.mp4"))
        video_counts[video_key] = len(video_files)
    
    print(f"  Parquet文件: {len(parquet_files)}")
    for video_key, count in video_counts.items():
        print(f"  {video_key}: {count} 视频文件")
    
    # 检查数量一致性
    expected_count = info["total_episodes"]
    if len(parquet_files) != expected_count:
        issues.append(f"Parquet文件数量不匹配: 期望{expected_count}, 实际{len(parquet_files)}")
    
    for video_key, count in video_counts.items():
        if count != expected_count:
            issues.append(f"{video_key}视频文件数量不匹配: 期望{expected_count}, 实际{count}")
    
    if len(episodes) != expected_count:
        issues.append(f"episodes.jsonl条目数量不匹配: 期望{expected_count}, 实际{len(episodes)}")
    
    if len(episodes_stats) != expected_count:
        issues.append(f"episodes_stats.jsonl条目数量不匹配: 期望{expected_count}, 实际{len(episodes_stats)}")
    
    # 3. 检查episode索引连续性
    print("\n3. 检查episode索引连续性...")
    
    expected_indices = set(range(expected_count))
    actual_indices = set(episodes.keys())
    
    if expected_indices != actual_indices:
        missing = expected_indices - actual_indices
        extra = actual_indices - expected_indices
        if missing:
            issues.append(f"缺失的episode索引: {sorted(missing)}")
        if extra:
            issues.append(f"多余的episode索引: {sorted(extra)}")
    
    # 4. 检查parquet文件内容一致性
    print("\n4. 检查parquet文件内容一致性...")
    
    total_frames_actual = 0
    for i, parquet_file in enumerate(parquet_files):
        filename_idx = int(parquet_file.stem.split('_')[1])
        
        df = pd.read_parquet(parquet_file)
        content_episode_idx = df['episode_index'].iloc[0]
        frame_count = len(df)
        
        # 检查文件名与内容的episode_index一致性
        if filename_idx != content_episode_idx:
            issues.append(f"{parquet_file.name}: 文件名episode_index({filename_idx}) != 内容episode_index({content_episode_idx})")
        
        # 检查与episodes.jsonl的长度一致性
        if content_episode_idx in episodes:
            expected_length = episodes[content_episode_idx]["length"]
            if frame_count != expected_length:
                issues.append(f"Episode {content_episode_idx}: parquet长度({frame_count}) != episodes.jsonl长度({expected_length})")
        
        total_frames_actual += frame_count
    
    # 检查总帧数
    if total_frames_actual != info["total_frames"]:
        issues.append(f"总帧数不匹配: info.json({info['total_frames']}) != 实际({total_frames_actual})")
    
    # 5. 检查index字段连续性
    print("\n5. 检查index字段连续性...")
    
    cumulative_frames = 0
    for i in range(expected_count):
        parquet_file = data_dir / f"episode_{i:06d}.parquet"
        if parquet_file.exists():
            df = pd.read_parquet(parquet_file)
            expected_start_index = cumulative_frames
            actual_start_index = df['index'].iloc[0]
            
            if expected_start_index != actual_start_index:
                issues.append(f"Episode {i}: index不连续, 期望起始{expected_start_index}, 实际{actual_start_index}")
            
            cumulative_frames += len(df)
    
    # 6. 检查tasks一致性
    print("\n6. 检查tasks一致性...")
    
    # 从episodes中收集所有tasks
    all_tasks_from_episodes = set()
    for ep in episodes.values():
        if "tasks" in ep:
            all_tasks_from_episodes.update(ep["tasks"])
    
    all_tasks_from_file = set(task["task"] for task in tasks.values())
    
    if all_tasks_from_episodes != all_tasks_from_file:
        issues.append(f"Tasks不一致: episodes中的tasks({all_tasks_from_episodes}) != tasks.jsonl中的tasks({all_tasks_from_file})")
    
    if len(tasks) != info["total_tasks"]:
        issues.append(f"Tasks数量不匹配: info.json({info['total_tasks']}) != tasks.jsonl({len(tasks)})")
    
    # 7. 报告结果
    print(f"\n=== 检查结果 ===")
    if issues:
        print(f"❌ 发现 {len(issues)} 个问题:")
        for i, issue in enumerate(issues, 1):
            print(f"  {i}. {issue}")
        return False
    else:
        print("✅ 所有检查都通过，数据集状态正常!")
        return True

if __name__ == "__main__":
    dataset_path = "/Users/<USER>/Documents/PyCharm/MacPythonEnv/lerobot_dual/tools/so101_dual_55"
    check_dataset_consistency(dataset_path)
