#!/usr/bin/env python

"""
测试修复后的remove_episodes.py功能
"""

import subprocess
import sys
import json
from pathlib import Path

def test_dataset_loading():
    """测试数据集加载功能"""
    print("=== 测试数据集加载功能 ===")
    
    dataset_path = "/Users/<USER>/Documents/PyCharm/MacPythonEnv/lerobot_dual/tools/so101_dual_55"
    
    # 检查数据集路径是否存在
    if not Path(dataset_path).exists():
        print(f"❌ 数据集路径不存在: {dataset_path}")
        return False
    
    print(f"✅ 数据集路径存在: {dataset_path}")
    
    # 检查必要的文件
    meta_dir = Path(dataset_path) / "meta"
    required_files = ["info.json", "episodes.jsonl", "tasks.jsonl"]
    
    for file_name in required_files:
        file_path = meta_dir / file_name
        if file_path.exists():
            print(f"✅ {file_name} 存在")
        else:
            print(f"❌ {file_name} 不存在")
            return False
    
    return True

def check_current_dataset_state():
    """检查当前数据集状态"""
    print("\n=== 检查当前数据集状态 ===")
    
    dataset_path = Path("/Users/<USER>/Documents/PyCharm/MacPythonEnv/lerobot_dual/tools/so101_dual_55")
    
    # 读取info.json
    info_path = dataset_path / "meta/info.json"
    with open(info_path, 'r') as f:
        info = json.load(f)
    
    print(f"总episodes数: {info['total_episodes']}")
    print(f"总frames数: {info['total_frames']}")
    print(f"总tasks数: {info['total_tasks']}")
    
    # 读取episodes.jsonl
    episodes_path = dataset_path / "meta/episodes.jsonl"
    episodes = []
    with open(episodes_path, 'r') as f:
        for line in f:
            episodes.append(json.loads(line))
    
    print(f"episodes.jsonl中的episodes数: {len(episodes)}")
    if episodes:
        print(f"Episode索引范围: {episodes[0]['episode_index']} - {episodes[-1]['episode_index']}")
    
    # 检查parquet文件
    data_dir = dataset_path / "data/chunk-000"
    parquet_files = list(data_dir.glob("episode_*.parquet"))
    print(f"Parquet文件数: {len(parquet_files)}")
    
    if parquet_files:
        first_file = min(parquet_files, key=lambda x: x.name)
        last_file = max(parquet_files, key=lambda x: x.name)
        print(f"Parquet文件范围: {first_file.name} - {last_file.name}")
    
    return len(episodes), len(parquet_files)

def test_dry_run():
    """测试干运行（不实际删除）"""
    print("\n=== 测试干运行 ===")
    
    # 这里我们只测试参数解析和初始加载，不实际执行删除
    cmd = [
        sys.executable, 
        "/Users/<USER>/Documents/PyCharm/MacPythonEnv/lerobot_dual/tools/remove_episodes.py",
        "--dataset", "/Users/<USER>/Documents/PyCharm/MacPythonEnv/lerobot_dual/tools/so101_dual_55",
        "--episodes", "0-2",  # 只删除前3个episodes作为测试
        "--help"  # 显示帮助而不执行
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        if "usage:" in result.stdout.lower():
            print("✅ 参数解析正常")
            return True
        else:
            print(f"❌ 参数解析异常: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("❌ 命令超时")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def show_recommended_usage():
    """显示推荐的使用方法"""
    print("\n=== 推荐的使用方法 ===")
    
    dataset_path = "/Users/<USER>/Documents/PyCharm/MacPythonEnv/lerobot_dual/tools/so101_dual_55"
    
    examples = [
        {
            "description": "删除前3个episodes（测试用）",
            "command": f"python lerobot_dual/tools/remove_episodes.py --dataset {dataset_path} --episodes 0-2 --backup"
        },
        {
            "description": "删除前45个episodes（您的原始需求）",
            "command": f"python lerobot_dual/tools/remove_episodes.py --dataset {dataset_path} --episodes 0-44 --backup"
        },
        {
            "description": "删除特定的episodes",
            "command": f"python lerobot_dual/tools/remove_episodes.py --dataset {dataset_path} --episodes 4,5,10-15 --backup"
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"{i}. {example['description']}:")
        print(f"   {example['command']}")
        print()
    
    print("⚠️  重要提示:")
    print("   - 建议先使用小范围的episodes进行测试（如0-2）")
    print("   - 始终使用--backup参数创建备份")
    print("   - 确保有足够的磁盘空间进行备份")

def main():
    print("🧪 测试修复后的remove_episodes.py功能")
    print("=" * 60)
    
    # 1. 测试数据集加载
    if not test_dataset_loading():
        print("\n❌ 数据集加载测试失败，请检查数据集路径和文件")
        return
    
    # 2. 检查当前状态
    episodes_count, parquet_count = check_current_dataset_state()
    
    # 3. 测试干运行
    test_dry_run()
    
    # 4. 显示推荐用法
    show_recommended_usage()
    
    print("\n🎯 测试总结:")
    print(f"   - 当前数据集有 {episodes_count} 个episodes")
    print(f"   - 当前有 {parquet_count} 个parquet文件")
    print("   - 修复后的代码应该能够:")
    print("     ✅ 避免重新创建LeRobotDataset")
    print("     ✅ 直接在原有dataset对象上修改")
    print("     ✅ 最后统一执行重新编号")
    print("     ✅ 不从HuggingFace Hub下载数据")
    
    print("\n💡 下一步:")
    print("   建议先用小范围测试，如: --episodes 0-2")

if __name__ == "__main__":
    main()
