# Remove Episodes Tool - 使用说明

这个工具用于从LeRobot数据集中删除指定的episodes，支持本地数据集和HuggingFace Hub数据集。

## 🚀 新功能

- ✅ 支持 `--dataset` 参数：直接指定本地数据集的绝对路径
- ✅ 支持 `--episodes` 参数：灵活指定要删除的episodes
- ✅ 完整的数据一致性修复：更新parquet文件内容、重新编号、更新所有元数据
- ✅ 互斥参数验证：`--repo-id` 和 `--dataset` 不能同时使用

## 📋 参数说明

### 必需参数（二选一）
- `--repo-id REPO_ID`: HuggingFace仓库ID（如 `lerobot/pusht`）
- `--dataset DATASET`: 本地数据集的绝对路径

### 必需参数
- `--episodes EPISODES`: 要删除的episodes，支持多种格式：
  - 单个episode: `4`
  - 多个episodes: `4,5,8`
  - 范围: `1-5` (删除1,2,3,4,5)
  - 混合: `1-3,5,7-9` (删除1,2,3,5,7,8,9)

### 可选参数
- `--root ROOT`: 本地根目录（仅与`--repo-id`一起使用）
- `--backup [BACKUP]`: 创建备份
- `--push-to-hub`: 推送到HuggingFace Hub（仅与`--repo-id`一起使用）
- `--private`: 设置仓库为私有
- `--tags`: 添加标签
- `--license`: 设置许可证

## 💡 使用示例

### 1. 使用本地数据集路径（推荐）

```bash
# 删除episodes 4和5
python lerobot_dual/tools/remove_episodes.py \
    --dataset /home/<USER>/.cache/huggingface/lerobot/fibot/so101_dual_55 \
    --episodes 4,5

# 删除episodes范围并创建备份
python lerobot_dual/tools/remove_episodes.py \
    --dataset /path/to/your/dataset \
    --episodes 1-3,7 \
    --backup

# 删除单个episode
python lerobot_dual/tools/remove_episodes.py \
    --dataset /path/to/your/dataset \
    --episodes 10
```

### 2. 使用HuggingFace仓库ID（原有功能）

```bash
# 从HuggingFace Hub删除episodes
python lerobot_dual/tools/remove_episodes.py \
    --repo-id lerobot/pusht \
    --episodes 1-5,7,10-12 \
    --push-to-hub 1

# 使用本地缓存
python lerobot_dual/tools/remove_episodes.py \
    --repo-id your/dataset \
    --root /path/to/local/cache \
    --episodes 4,5
```

## 🔧 修复的问题

这个版本修复了原始代码中的几个关键问题：

1. **Parquet文件内容更新**: 不仅重命名文件，还更新文件内部的`episode_index`和`index`字段
2. **Tasks.jsonl重新生成**: 删除episodes后重新计算任务索引
3. **完整的元数据更新**: 更新`info.json`中的所有统计信息
4. **Index字段连续性**: 确保全局`index`字段在所有episodes中保持连续

## ⚠️ 注意事项

1. **备份建议**: 在删除episodes前建议使用`--backup`参数创建备份
2. **路径格式**: `--dataset`参数需要提供绝对路径
3. **权限要求**: 确保对数据集目录有读写权限
4. **数据一致性**: 工具会自动检查并修复所有相关文件的一致性

## 🧪 测试

运行测试脚本验证功能：

```bash
python lerobot_dual/tools/test_new_parameters.py
```

运行数据集一致性检查：

```bash
python lerobot_dual/tools/comprehensive_dataset_check.py
```

## 📊 处理的文件类型

- ✅ **data/*.parquet**: 更新文件名和内容
- ✅ **videos/*.mp4**: 重命名文件
- ✅ **meta/episodes.jsonl**: 更新episode信息
- ✅ **meta/episodes_stats.jsonl**: 更新统计信息
- ✅ **meta/tasks.jsonl**: 重新生成任务索引
- ✅ **meta/info.json**: 更新所有统计信息

## 🎯 示例输出

```
=== 数据集一致性全面检查 ===
数据集路径: /path/to/dataset

1. 读取metadata文件...
  info.json: total_episodes=50, total_frames=6500, total_tasks=1
  episodes.jsonl: 50 episodes
  episodes_stats.jsonl: 50 stats
  tasks.jsonl: 1 tasks

2. 检查文件数量一致性...
  Parquet文件: 50
  observation.images.top: 50 视频文件
  observation.images.left_wrist: 50 视频文件
  observation.images.right_wrist: 50 视频文件

=== 检查结果 ===
✅ 所有检查都通过，数据集状态正常!
```
