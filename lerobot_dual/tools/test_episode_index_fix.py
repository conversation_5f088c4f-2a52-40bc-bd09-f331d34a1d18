#!/usr/bin/env python

"""
测试脚本：验证remove_episodes.py中episode_index的修复
"""

import pandas as pd
import json
from pathlib import Path

def test_episode_index_consistency():
    """测试episode_index的一致性"""
    
    dataset_root = Path("/Users/<USER>/Documents/PyCharm/MacPythonEnv/lerobot_dual/tools/so101_dual_55")
    
    print("=== 测试 Episode Index 一致性 ===")
    
    # 1. 读取episodes.jsonl
    episodes_file = dataset_root / "meta/episodes.jsonl"
    episodes_from_jsonl = {}
    
    with open(episodes_file, 'r') as f:
        for line in f:
            ep = json.loads(line)
            episodes_from_jsonl[ep["episode_index"]] = ep
    
    print(f"从episodes.jsonl读取到 {len(episodes_from_jsonl)} 个episodes")
    print(f"Episode indices: {sorted(episodes_from_jsonl.keys())}")
    
    # 2. 检查parquet文件
    data_dir = dataset_root / "data/chunk-000"
    parquet_files = sorted(data_dir.glob("episode_*.parquet"))
    
    print(f"\n找到 {len(parquet_files)} 个parquet文件")
    
    inconsistencies = []
    
    for i, parquet_file in enumerate(parquet_files):
        # 从文件名提取episode index
        filename_episode_idx = int(parquet_file.stem.split('_')[1])
        
        # 从文件内容读取episode index
        df = pd.read_parquet(parquet_file)
        content_episode_idx = df['episode_index'].iloc[0]
        
        # 检查index字段的连续性
        expected_start_index = sum(episodes_from_jsonl[j]["length"] for j in range(i))
        actual_start_index = df['index'].iloc[0]
        
        print(f"文件 {parquet_file.name}:")
        print(f"  文件名episode_index: {filename_episode_idx}")
        print(f"  内容episode_index: {content_episode_idx}")
        print(f"  期望起始index: {expected_start_index}")
        print(f"  实际起始index: {actual_start_index}")
        
        if filename_episode_idx != content_episode_idx:
            inconsistencies.append({
                'file': parquet_file.name,
                'filename_idx': filename_episode_idx,
                'content_idx': content_episode_idx
            })
        
        if expected_start_index != actual_start_index:
            inconsistencies.append({
                'file': parquet_file.name,
                'issue': 'index_discontinuity',
                'expected_start': expected_start_index,
                'actual_start': actual_start_index
            })
    
    # 3. 报告结果
    if inconsistencies:
        print(f"\n❌ 发现 {len(inconsistencies)} 个不一致问题:")
        for issue in inconsistencies:
            print(f"  {issue}")
        return False
    else:
        print(f"\n✅ 所有episode_index和index字段都是一致的!")
        return True

def show_detailed_episode_info():
    """显示详细的episode信息"""
    
    dataset_root = Path("/Users/<USER>/Documents/PyCharm/MacPythonEnv/lerobot_dual/tools/so101_dual_55")
    
    print("\n=== 详细Episode信息 ===")
    
    # 读取前3个parquet文件的详细信息
    data_dir = dataset_root / "data/chunk-000"
    parquet_files = sorted(data_dir.glob("episode_*.parquet"))[:3]
    
    for parquet_file in parquet_files:
        df = pd.read_parquet(parquet_file)
        print(f"\n文件: {parquet_file.name}")
        print(f"  Episode index: {df['episode_index'].unique()}")
        print(f"  Frame count: {len(df)}")
        print(f"  Index range: {df['index'].min()} - {df['index'].max()}")
        print(f"  Frame index range: {df['frame_index'].min()} - {df['frame_index'].max()}")

if __name__ == "__main__":
    # 运行测试
    is_consistent = test_episode_index_consistency()
    show_detailed_episode_info()
    
    if not is_consistent:
        print("\n🔧 需要运行修复后的remove_episodes.py来解决这些问题")
    else:
        print("\n🎉 数据集状态正常!")
